import { PrismaClient, TenantUser, User } from "../generated/prisma";
import { createHash } from "crypto";
import { redis, REDIS_KEYS, CACHE_TTL } from "@workspace/ioredis";
import jwt from "jsonwebtoken";
import { AUTH_CONSTANTS } from "../constants/client-auth.constants";

// JWT载荷接口
interface JWTPayload {
  userId: string;
  email: string;
  username: string;
  tenantId?: string; // 当前租户ID
  role?: string; // 当前租户下的角色
  sessionId: string;
  type:
    | typeof AUTH_CONSTANTS.TOKEN_TYPES.ACCESS
    | typeof AUTH_CONSTANTS.TOKEN_TYPES.REFRESH; // Token类型
  iat?: number;
  exp?: number;
}

// 租户信息接口
interface TenantInfo {
  id: string;
  name: string;
  type: string;
  role: string;
}

// 登录输入
export interface LoginInput {
  email: string;
  password: string;
  remember?: boolean;
}

// 登录结果
export interface LoginResult {
  success: boolean;
  data?: {
    user: User;
    token: string;
    refreshToken: string;
    expiresAt: string;
    tenants: TenantInfo[]; // 用户的租户列表
    needTenantSelection: boolean; // 是否需要选择租户
  };
  message?: string;
  errors?: Record<string, string>;
}

// 会话验证结果
export interface SessionResult {
  success: boolean;
  user?: User;
  message?: string;
}

// 登出结果
export interface LogoutResult {
  success: boolean;
  message?: string;
}

// 租户切换结果
export interface TenantSwitchResult {
  success: boolean;
  data?: {
    token: string;
    refreshToken: string;
    expiresAt: string;
    currentTenant: TenantUser & { customDomain: string };
  };
  message?: string;
}

// 密码重置请求
export interface ForgotPasswordInput {
  email: string;
}

// 密码重置结果
export interface ForgotPasswordResult {
  success: boolean;
  message?: string;
}

// 重置密码输入
export interface ResetPasswordInput {
  token: string;
  newPassword: string;
}

// 重置密码结果
export interface ResetPasswordResult {
  success: boolean;
  message?: string;
}

// 修改密码输入
export interface ChangePasswordInput {
  currentPassword: string;
  newPassword: string;
}

// 修改密码结果
export interface ChangePasswordResult {
  success: boolean;
  message?: string;
}

export class ClientAuthService {
  constructor(private prisma: PrismaClient) {}

  /**
   * MD5加密
   */
  private hashPassword(password: string): string {
    return createHash("md5").update(password).digest("hex");
  }

  /**
   * 生成JWT token
   */
  private generateJWT(payload: Omit<JWTPayload, "iat" | "exp">): string {
    return jwt.sign(payload, AUTH_CONSTANTS.JWT.SECRET, {
      expiresIn: AUTH_CONSTANTS.JWT.ACCESS_EXPIRES_IN as any,
    });
  }

  /**
   * 生成Refresh Token
   */
  private generateRefreshToken(
    payload: Omit<JWTPayload, "iat" | "exp">,
  ): string {
    return jwt.sign(payload, AUTH_CONSTANTS.JWT.SECRET, {
      expiresIn: AUTH_CONSTANTS.JWT.REFRESH_EXPIRES_IN as any,
    });
  }

  /**
   * 验证JWT token
   */
  private verifyJWT(token: string): JWTPayload | null {
    try {
      const decoded = jwt.verify(
        token,
        AUTH_CONSTANTS.JWT.SECRET,
      ) as JWTPayload;
      return decoded;
    } catch (error) {
      console.error("JWT验证失败:", error);
      return null;
    }
  }

  async login(
    input: LoginInput,
    ip?: string,
    userAgent?: string,
  ): Promise<LoginResult> {
    try {
      // 1. 查找用户
      const user = await this.prisma.user.findUnique({
        where: { email: input.email.toLowerCase() },
      });

      if (!user) {
        return {
          success: false,
          message: "用户名或密码错误",
        };
      }

      // 2. 验证密码 - 使用MD5加密验证
      const hashedInputPassword = this.hashPassword(input.password);
      if (hashedInputPassword !== user.password) {
        return {
          success: false,
          message: "用户名或密码错误",
        };
      }

      // 3. 检查用户状态
      if (!user.isActive) {
        return {
          success: false,
          message: "账户已被禁用，请联系管理员",
        };
      }

      // 4. 获取用户的租户关系
      const tenantUsers = await this.prisma.tenantUser.findMany({
        where: {
          userId: user.id,
          isActive: true,
        },
        include: {
          tenant: true,
        },
      });

      // 5. 构建租户信息
      const tenants: TenantInfo[] = tenantUsers.map((tu) => ({
        id: tu.tenant.id,
        name: tu.tenant.name,
        type: tu.tenant.type,
        role: tu.role,
      }));

      // 6. 生成会话ID
      const sessionId = createHash("md5")
        .update(`${user.id}-${Date.now()}-${Math.random()}`)
        .digest("hex");

      // 7. 创建Session记录
      const session = await this.prisma.session.create({
        data: {
          userId: user.id,
          token: sessionId,
          expiresAt: new Date(
            Date.now() +
              (input.remember
                ? AUTH_CONSTANTS.TOKEN.REMEMBER_EXPIRES_IN
                : AUTH_CONSTANTS.TOKEN.ACCESS_EXPIRES_IN),
          ), // 记住我30天，否则2小时
        },
      });

      // 8. 生成JWT Token
      const tokenPayload: JWTPayload = {
        userId: user.id,
        email: user.email,
        username: user.username,
        sessionId: session.id,
        type: AUTH_CONSTANTS.TOKEN_TYPES.ACCESS,
      };

      const accessToken = this.generateJWT(tokenPayload);

      // 9. 生成Refresh Token
      const refreshTokenPayload: JWTPayload = {
        userId: user.id,
        email: user.email,
        username: user.username,
        sessionId: session.id,
        type: AUTH_CONSTANTS.TOKEN_TYPES.REFRESH,
      };

      const refreshToken = this.generateRefreshToken(refreshTokenPayload);

      // 10. 更新用户最后登录时间
      await this.prisma.user.update({
        where: { id: user.id },
        data: { lastLoginAt: new Date() },
      });

      // 11. 缓存用户信息到Redis
      const cacheKey = `${REDIS_KEYS.SESSION(session.id)}`;
      await redis.setex(
        cacheKey,
        CACHE_TTL.SESSION,
        JSON.stringify({
          userId: user.id,
          email: user.email,
          username: user.username,
          tenants,
          ip,
          userAgent,
          loginAt: new Date().toISOString(),
        }),
      );

      return {
        success: true,
        data: {
          user: {
            ...user,
            password: undefined as any, // 不返回密码
          },
          token: accessToken,
          refreshToken,
          expiresAt: new Date(
            Date.now() + AUTH_CONSTANTS.TOKEN.ACCESS_EXPIRES_IN,
          ).toISOString(), // 2小时后过期
          tenants,
          needTenantSelection: tenants.length > 1, // 多个租户需要选择
        },
      };
    } catch (error) {
      console.error("Login error:", error);
      return {
        success: false,
        message: "登录失败，请稍后重试",
      };
    }
  }

  /**
   * 刷新访问令牌
   */
  async refreshToken(refreshToken: string): Promise<LoginResult> {
    try {
      // 1. 验证refresh token
      const decoded = this.verifyJWT(refreshToken);
      if (!decoded || decoded.type !== "refresh") {
        return {
          success: false,
          message: "无效的刷新令牌",
        };
      }

      // 2. 检查会话是否存在且有效
      const session = await this.prisma.session.findUnique({
        where: { id: decoded.sessionId },
        include: { user: true },
      });

      if (!session) {
        return {
          success: false,
          message: "会话不存在",
        };
      }

      // 3. 检查会话是否过期
      if (session.expiresAt < new Date()) {
        await this.prisma.session.delete({
          where: { id: session.id },
        });
        return {
          success: false,
          message: "会话已过期",
        };
      }

      // 4. 检查用户状态
      if (!session.user.isActive) {
        return {
          success: false,
          message: "账户已被禁用",
        };
      }

      // 5. 获取用户的租户关系
      const tenantUsers = await this.prisma.tenantUser.findMany({
        where: {
          userId: session.user.id,
          isActive: true,
        },
        include: {
          tenant: true,
        },
      });

      const tenants: TenantInfo[] = tenantUsers.map((tu) => ({
        id: tu.tenant.id,
        name: tu.tenant.name,
        type: tu.tenant.type,
        role: tu.role,
      }));

      // 6. 生成新的访问令牌
      const newTokenPayload: JWTPayload = {
        userId: session.user.id,
        email: session.user.email,
        username: session.user.username,
        sessionId: session.id,
        type: AUTH_CONSTANTS.TOKEN_TYPES.ACCESS,
      };

      const newAccessToken = this.generateJWT(newTokenPayload);

      // 7. 生成新的刷新令牌（Token轮换）
      const newRefreshTokenPayload: JWTPayload = {
        userId: session.user.id,
        email: session.user.email,
        username: session.user.username,
        sessionId: session.id,
        type: AUTH_CONSTANTS.TOKEN_TYPES.REFRESH,
      };

      const newRefreshToken = this.generateRefreshToken(newRefreshTokenPayload);

      // 8. 更新缓存
      const cacheKey = `${REDIS_KEYS.SESSION(session.id)}`;
      const cachedData = await redis.get(cacheKey);
      if (cachedData) {
        const sessionData = JSON.parse(cachedData);
        sessionData.tenants = tenants;
        await redis.setex(
          cacheKey,
          CACHE_TTL.SESSION,
          JSON.stringify(sessionData),
        );
      }

      return {
        success: true,
        data: {
          user: {
            ...session.user,
            password: undefined as any,
          },
          token: newAccessToken,
          refreshToken: newRefreshToken,
          expiresAt: new Date(
            Date.now() + AUTH_CONSTANTS.TOKEN.ACCESS_EXPIRES_IN,
          ).toISOString(),
          tenants,
          needTenantSelection: tenants.length > 1,
        },
      };
    } catch (error) {
      console.error("Refresh token error:", error);
      return {
        success: false,
        message: "令牌刷新失败",
      };
    }
  }

  /**
   * 用户登出
   */
  async logout(token: string): Promise<LogoutResult> {
    try {
      // 1. 验证JWT token获取会话ID
      const decoded = this.verifyJWT(token);
      if (!decoded) {
        return {
          success: false,
          message: "无效的令牌",
        };
      }

      // 2. 删除数据库中的会话记录
      await this.prisma.session.deleteMany({
        where: { id: decoded.sessionId },
      });

      // 3. 删除Redis缓存
      try {
        const cacheKey = `${REDIS_KEYS.SESSION(decoded.sessionId)}`;
        await redis.del(cacheKey);
      } catch (redisError) {
        console.error("Redis删除失败:", redisError);
        // Redis失败不影响登出流程
      }

      return {
        success: true,
        message: "退出成功",
      };
    } catch (error) {
      console.error("Logout error:", error);
      return {
        success: false,
        message: "退出失败",
      };
    }
  }

  /**
   * 验证用户会话
   */
  async verifySession(token: string): Promise<SessionResult> {
    try {
      // 1. 验证JWT token
      const decoded = this.verifyJWT(token);
      if (!decoded || decoded.type !== "access") {
        return {
          success: false,
          message: "无效的访问令牌",
        };
      }

      // 2. 先从Redis缓存中获取
      const cacheKey = `${REDIS_KEYS.SESSION(decoded.sessionId)}`;
      try {
        const cachedData = await redis.get(cacheKey);
        if (cachedData) {
          const sessionData = JSON.parse(cachedData);

          // 从数据库获取最新用户信息
          const user = await this.prisma.user.findUnique({
            where: { id: sessionData.userId },
          });

          if (!user || !user.isActive) {
            return {
              success: false,
              message: "用户不存在或已被禁用",
            };
          }

          return {
            success: true,
            user: {
              ...user,
              password: undefined as any,
            },
          };
        }
      } catch (redisError) {
        console.error("Redis读取失败:", redisError);
      }

      // 3. 如果缓存中没有，从数据库验证
      const session = await this.prisma.session.findUnique({
        where: { id: decoded.sessionId },
        include: { user: true },
      });

      if (!session) {
        return {
          success: false,
          message: "会话不存在",
        };
      }

      // 4. 检查会话是否过期
      if (session.expiresAt < new Date()) {
        await this.prisma.session.delete({
          where: { id: session.id },
        });
        return {
          success: false,
          message: "会话已过期",
        };
      }

      // 5. 检查用户状态
      if (!session.user.isActive) {
        return {
          success: false,
          message: "账户已被禁用",
        };
      }

      return {
        success: true,
        user: {
          ...session.user,
          password: undefined as any,
        },
      };
    } catch (error) {
      console.error("Verify session error:", error);
      return {
        success: false,
        message: "会话验证失败",
      };
    }
  }

  /**
   * 切换租户上下文
   */
  async switchTenant(
    token: string,
    tenantId: string,
  ): Promise<TenantSwitchResult> {
    try {
      // 1. 验证当前token
      const decoded = this.verifyJWT(token);
      if (!decoded) {
        return {
          success: false,
          message: "无效的访问令牌",
        };
      }

      // 2. 验证用户是否属于目标租户
      const tenantUser = await this.prisma.tenantUser.findUnique({
        where: {
          userId_tenantId: {
            userId: decoded.userId,
            tenantId: tenantId,
          },
          isActive: true,
        },
        include: {
          tenant: true,
        },
      });

      if (!tenantUser) {
        return {
          success: false,
          message: "您没有访问该工作区的权限",
        };
      }

      // 3. 构建租户信息
      const currentTenant: TenantInfo = {
        id: tenantUser.tenant.id,
        name: tenantUser.tenant.name,
        type: tenantUser.tenant.type,
        role: tenantUser.role,
      };

      // 4. 生成新的JWT Token (包含租户上下文)
      const newTokenPayload: JWTPayload = {
        userId: decoded.userId,
        email: decoded.email,
        username: decoded.username,
        tenantId: tenantId,
        role: tenantUser.role,
        sessionId: decoded.sessionId,
        type: AUTH_CONSTANTS.TOKEN_TYPES.ACCESS,
      };

      const newAccessToken = this.generateJWT(newTokenPayload);

      // 5. 生成新的Refresh Token
      const newRefreshTokenPayload: JWTPayload = {
        userId: decoded.userId,
        email: decoded.email,
        username: decoded.username,
        tenantId: tenantId,
        role: tenantUser.role,
        sessionId: decoded.sessionId,
        type: AUTH_CONSTANTS.TOKEN_TYPES.REFRESH,
      };

      const newRefreshToken = this.generateRefreshToken(newRefreshTokenPayload);

      // 6. 更新Redis缓存中的租户上下文
      const cacheKey = `${REDIS_KEYS.SESSION(decoded.sessionId)}`;
      try {
        const cachedData = await redis.get(cacheKey);
        if (cachedData) {
          const sessionData = JSON.parse(cachedData);
          sessionData.currentTenant = currentTenant;
          await redis.setex(
            cacheKey,
            CACHE_TTL.SESSION,
            JSON.stringify(sessionData),
          );
        }
      } catch (redisError) {
        console.error("Redis更新失败:", redisError);
      }

      return {
        success: true,
        data: {
          token: newAccessToken,
          refreshToken: newRefreshToken,
          expiresAt: new Date(
            Date.now() + AUTH_CONSTANTS.TOKEN.ACCESS_EXPIRES_IN,
          ).toISOString(),
          currentTenant: tenantUser.tenant as any, // 返回完整的租户用户信息
        },
      };
    } catch (error) {
      console.error("Switch tenant error:", error);
      return {
        success: false,
        message: "切换工作区失败",
      };
    }
  }
}
