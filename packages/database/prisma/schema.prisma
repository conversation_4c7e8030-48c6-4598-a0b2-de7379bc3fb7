// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
  output   = "../src/generated/prisma"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// ==================== 基础枚举定义 ====================

enum UserRole {
  SUPER_ADMIN // 超级管理员
  ADMIN // 平台管理员
  OPERATOR // 运营人员
  TENANT_ADMIN // 租户管理员
  PLANNER // 规划师
  STUDENT // 学生
  PARENT // 家长
  COMMUNITY_USER // 社区用户
  GUEST // 访客
}

enum TenantType {
  INSTITUTION // 机构
  INDIVIDUAL // 个人规划师
}

enum TenantStatus {
  PENDING // 待审核
  ACTIVE // 激活
  SUSPENDED // 暂停
  EXPIRED // 过期
}

// ==================== 订阅系统枚举 ====================

enum PlanType {
  FREE // 免费版
  PERSONAL // 个人版
  INSTITUTION // 机构版
  CUSTOM // 定制版
}

enum BillingCycle {
  MONTHLY // 月付
  QUARTERLY // 季付
  YEARLY // 年付
  LIFETIME // 终身
}

enum SubscriptionStatus {
  TRIAL // 试用中
  ACTIVE // 生效中
  PAST_DUE // 逾期
  CANCELLED // 已取消
  EXPIRED // 已过期
}

// ==================== 社区系统枚举 ====================

enum PostType {
  ARTICLE // 文章
  SHARE // 分享
  QUESTION // 问题
  ANNOUNCEMENT // 公告
  EXPERIENCE // 经验分享
}

enum PostStatus {
  DRAFT // 草稿
  PENDING_REVIEW // 待审核
  PUBLISHED // 已发布
  HIDDEN // 隐藏
  DELETED // 已删除
}

enum CourseStatus {
  DRAFT // 草稿
  REVIEWING // 审核中
  PUBLISHED // 已发布
  OFFLINE // 已下架
}

enum CourseLevel {
  BEGINNER // 初级
  INTERMEDIATE // 中级
  ADVANCED // 高级
  ALL // 全部级别
}

enum ProductType {
  STUDY_CAMP // 研学营
  CERTIFICATE // 证书
  BACKGROUND // 背景提升
  SCHOOL_LINK // 名校链接
  INSTITUTION // 机构提升
  OTHER // 其他
}

enum ProductStatus {
  DRAFT // 草稿
  ACTIVE // 上架
  INACTIVE // 下架
  SOLD_OUT // 售罄
}

enum InquiryStatus {
  PENDING // 待处理
  CONTACTING // 联系中
  NEGOTIATING // 洽谈中
  COMPLETED // 已完成
  CANCELLED // 已取消
}

// ==================== 任务系统枚举 ====================

enum TaskStatus {
  PENDING
  PROCESSING
  COMPLETED
  FAILED
  CANCELLED
}

enum TaskType {
  EMAIL_CAMPAIGN
  SMS_CAMPAIGN
  REPORT_GENERATION
  DATA_EXPORT
  SYSTEM_MAINTENANCE
}

// ==================== 通知系统枚举 ====================

enum NotificationType {
  SYSTEM // 系统通知
  APPOINTMENT // 预约提醒
  TASK // 任务通知
  COMMENT // 评论通知
  LIKE // 点赞通知
  FOLLOW // 关注通知
  POST // 帖子相关
}

// ==================== 管理后台枚举 ====================

enum AdminStatus {
  ACTIVE
  INACTIVE
  SUSPENDED
}

// ==================== 用户与认证系统 ====================

// 用户表（社区和工作区共用）
model User {
  id            String    @id @default(cuid())
  email         String    @unique
  username      String    @unique
  password      String
  name          String
  avatar        String?
  phone         String?
  bio           String? // 个人简介
  isActive      Boolean   @default(true)
  emailVerified Boolean   @default(false)
  lastLoginAt   DateTime?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // 社区相关（不需要租户隔离）
  posts     Post[]
  comments  Comment[]
  likes     Like[]
  favorites Favorite[]
  followers Follow[]   @relation("Following")
  following Follow[]   @relation("Followers")
  resources Resource[] // 上传的资源

  // 课程相关
  courseEnrollments CourseEnrollment[]
  lessonProgress    LessonProgress[]
  courseReviews     CourseReview[]

  // 产品询价
  productInquiries ProductInquiry[]

  // 通用关系
  notifications Notification[]
  sessions      Session[]

  // 工作区相关（需要租户隔离）
  tenantUsers TenantUser[] // 用户可以属于多个租户

  @@index([email])
  @@index([username])
}

// 会话管理
model Session {
  id        String   @id @default(cuid())
  userId    String
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  token     String   @unique
  expiresAt DateTime
  createdAt DateTime @default(now())

  @@index([userId])
  @@index([token])
}

// ==================== 租户系统（工作区专用） ====================

// 租户表
model Tenant {
  id           String       @id @default(cuid())
  name         String
  type         TenantType
  status       TenantStatus @default(PENDING)
  logo         String?
  description  String?
  contactName  String
  contactPhone String
  contactEmail String
  address      String?

  // 自定义配置
  settings     Json? // 租户自定义配置
  customDomain String? @unique // 自定义域名

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // 关系 - 仅工作区相关
  subscription        Subscription? // 当前订阅
  subscriptionHistory SubscriptionHistory[]
  tenantUsers         TenantUser[] // 租户下的用户
  students            Student[]
  planners            Planner[]
  aiTools             AITool[]
  tasks               Task[]
  pages               LandingPage[]
  usageRecords        UsageRecord[]
  invoices            Invoice[]
  productInquiries    ProductInquiry[] // 产品询价
  couponUsages        CouponUsage[]

  @@index([status])
}

// 租户用户关联表（用户在特定租户下的角色）
model TenantUser {
  id       String @id @default(cuid())
  userId   String
  user     User   @relation(fields: [userId], references: [id], onDelete: Cascade)
  tenantId String
  tenant   Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  role     UserRole // 用户在该租户下的角色
  isActive Boolean  @default(true)
  joinedAt DateTime @default(now())

  // 关系
  planner        Planner? // 如果是规划师角色
  studentProfile Student? // 如果是学生角色
  aiUsageRecords AIUsageRecord[]
  documents      Document[]
  tasks          Task[]
  pages          LandingPage[]
  auditLogs      AuditLog[]

  @@unique([userId, tenantId])
  @@index([userId])
  @@index([tenantId])
}

// ==================== 订阅与计费系统 ====================

// 订阅计划表
model SubscriptionPlan {
  id          String   @id @default(cuid())
  name        String // 计划名称：免费版、个人专业版、机构旗舰版
  code        String   @unique // 计划代码：free, personal, institution
  type        PlanType
  description String?

  // 功能配置
  features Json // 功能列表配置
  limits   Json // 限制配置

  // 权益配置
  maxUsers      Int @default(1)
  maxStudents   Int @default(10)
  maxPlanners   Int @default(1)
  maxStorage    Int @default(1024) // MB
  maxAIRequests Int @default(100) // 每月AI请求次数

  // 功能开关
  hasStudentCRM      Boolean @default(true)
  hasAITools         Boolean @default(false)
  hasDataDashboard   Boolean @default(false)
  hasScheduledTasks  Boolean @default(false)
  hasLandingPage     Boolean @default(false)
  hasCommunity       Boolean @default(true)
  hasCustomBranding  Boolean @default(false)
  hasAPIAccess       Boolean @default(false)
  hasPrioritySupport Boolean @default(false)

  // 显示配置
  displayOrder Int     @default(0)
  isPopular    Boolean @default(false) // 是否推荐
  badge        String? // 标签：如"最受欢迎"、"限时优惠"

  // 状态
  isActive  Boolean @default(true)
  isVisible Boolean @default(true) // 是否在定价页显示

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // 关系
  prices        SubscriptionPrice[]
  subscriptions Subscription[]
  benefits      PlanBenefit[]

  @@index([code])
  @@index([type])
}

// 订阅价格表
model SubscriptionPrice {
  id     String           @id @default(cuid())
  planId String
  plan   SubscriptionPlan @relation(fields: [planId], references: [id], onDelete: Cascade)

  billingCycle  BillingCycle
  price         Decimal      @db.Decimal(10, 2)
  originalPrice Decimal?     @db.Decimal(10, 2) // 原价，用于显示折扣
  currency      String       @default("CNY")

  // 优惠信息
  discount        Int? // 折扣百分比
  discountEndDate DateTime? // 折扣结束时间

  // 试用配置
  trialDays Int @default(0)

  isActive Boolean @default(true)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([planId, billingCycle])
  @@index([planId])
}

// 计划权益说明表
model PlanBenefit {
  id     String           @id @default(cuid())
  planId String
  plan   SubscriptionPlan @relation(fields: [planId], references: [id], onDelete: Cascade)

  title       String // 权益标题
  description String? // 权益详细说明
  icon        String? // 图标
  order       Int     @default(0)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([planId])
}

// 订阅表
model Subscription {
  id       String @id @default(cuid())
  tenantId String @unique
  tenant   Tenant @relation(fields: [tenantId], references: [id])

  planId String
  plan   SubscriptionPlan @relation(fields: [planId], references: [id])

  // 订阅信息
  status       SubscriptionStatus @default(TRIAL)
  billingCycle BillingCycle
  currentPrice Decimal            @db.Decimal(10, 2)

  // 时间信息
  trialEndDate DateTime? // 试用结束时间
  startDate    DateTime  @default(now())
  endDate      DateTime // 订阅结束时间
  cancelledAt  DateTime? // 取消时间

  // 自定义限制（可覆盖计划默认值）
  customLimits Json? // 自定义限制配置

  // 支付信息
  lastPaymentDate DateTime?
  nextPaymentDate DateTime?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // 关系
  payments Payment[]

  @@index([tenantId])
  @@index([planId])
  @@index([status])
}

// 订阅历史表
model SubscriptionHistory {
  id       String @id @default(cuid())
  tenantId String
  tenant   Tenant @relation(fields: [tenantId], references: [id])

  planName     String
  planType     PlanType
  billingCycle BillingCycle
  price        Decimal      @db.Decimal(10, 2)

  startDate DateTime
  endDate   DateTime

  // 变更信息
  action String // CREATED, UPGRADED, DOWNGRADED, RENEWED, CANCELLED
  reason String? // 变更原因

  createdAt DateTime @default(now())

  @@index([tenantId])
  @@index([createdAt])
}

// 用量记录表
model UsageRecord {
  id       String @id @default(cuid())
  tenantId String
  tenant   Tenant @relation(fields: [tenantId], references: [id])

  // 用量类型
  type   String // USERS, STUDENTS, AI_REQUESTS, STORAGE, etc.
  amount Int // 使用量

  // 时间周期
  period     String // 2024-01, 2024-01-15, etc.
  periodType String // DAILY, MONTHLY

  createdAt DateTime @default(now())

  @@unique([tenantId, type, period, periodType])
  @@index([tenantId])
  @@index([period])
}

// 支付记录表
model Payment {
  id             String       @id @default(cuid())
  subscriptionId String
  subscription   Subscription @relation(fields: [subscriptionId], references: [id])

  // 支付信息
  amount        Decimal @db.Decimal(10, 2)
  currency      String  @default("CNY")
  method        String // ALIPAY, WECHAT, BANK_TRANSFER, etc.
  transactionId String? // 第三方支付流水号

  // 状态
  status String // PENDING, SUCCESS, FAILED, REFUNDED
  paidAt DateTime?

  // 发票
  invoiceId String?  @unique
  invoice   Invoice?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([subscriptionId])
  @@index([status])
}

// 发票表
model Invoice {
  id       String @id @default(cuid())
  tenantId String
  tenant   Tenant @relation(fields: [tenantId], references: [id])

  // 发票信息
  invoiceNo   String  @unique
  amount      Decimal @db.Decimal(10, 2)
  tax         Decimal @db.Decimal(10, 2)
  totalAmount Decimal @db.Decimal(10, 2)

  // 开票信息
  title       String // 发票抬头
  taxNumber   String? // 税号
  address     String?
  phone       String?
  bankName    String?
  bankAccount String?

  // 状态
  status   String // PENDING, ISSUED, CANCELLED
  issuedAt DateTime?

  // 关联支付
  paymentId String  @unique
  payment   Payment @relation(fields: [paymentId], references: [id])

  // 文件
  pdfUrl String?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([tenantId])
  @@index([invoiceNo])
}

// 优惠券表
model Coupon {
  id          String  @id @default(cuid())
  code        String  @unique
  description String?

  // 优惠类型
  discountType  String // PERCENTAGE, FIXED_AMOUNT
  discountValue Decimal @db.Decimal(10, 2)

  // 适用范围
  applicablePlans String[] // 适用的计划ID列表
  minAmount       Decimal? @db.Decimal(10, 2) // 最低消费金额

  // 使用限制
  maxUses          Int? // 最大使用次数
  maxUsesPerTenant Int  @default(1) // 每个租户最大使用次数
  usedCount        Int  @default(0)

  // 有效期
  validFrom  DateTime
  validUntil DateTime

  isActive Boolean @default(true)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // 关系
  usageHistory CouponUsage[]

  @@index([code])
}

// 优惠券使用记录表
model CouponUsage {
  id       String @id @default(cuid())
  couponId String
  coupon   Coupon @relation(fields: [couponId], references: [id])
  tenantId String
  tenant   Tenant @relation(fields: [tenantId], references: [id])

  // 使用信息
  discountAmount Decimal @db.Decimal(10, 2)
  paymentId      String?

  usedAt DateTime @default(now())

  @@unique([couponId, tenantId])
  @@index([couponId])
  @@index([tenantId])
}

// ==================== 社区系统（公开） ====================

// 分类表（社区公用）
model Category {
  id          String     @id @default(cuid())
  name        String
  slug        String     @unique
  description String?
  parentId    String?
  parent      Category?  @relation("CategoryTree", fields: [parentId], references: [id])
  children    Category[] @relation("CategoryTree")
  order       Int        @default(0)
  isActive    Boolean    @default(true)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // 关系
  posts     Post[]
  resources Resource[]

  @@index([slug])
  @@index([parentId])
}

// 标签表（社区公用）
model Tag {
  id          String  @id @default(cuid())
  name        String  @unique
  slug        String  @unique
  description String?
  usageCount  Int     @default(0) // 使用次数

  createdAt DateTime @default(now())

  // 关系
  posts     PostTag[]
  resources ResourceTag[]

  @@index([slug])
  @@index([usageCount])
}

// 帖子表（社区公开内容）
model Post {
  id            String     @id @default(cuid())
  title         String
  content       String     @db.Text
  summary       String?
  cover         String?
  type          PostType
  status        PostStatus @default(DRAFT)
  viewCount     Int        @default(0)
  isTop         Boolean    @default(false)
  isRecommended Boolean    @default(false)
  isOriginal    Boolean    @default(true) // 是否原创

  // 作者信息
  authorId     String
  author       User    @relation(fields: [authorId], references: [id])
  authorName   String? // 冗余字段，提高查询性能
  authorAvatar String? // 冗余字段，提高查询性能

  // 分类
  categoryId String?
  category   Category? @relation(fields: [categoryId], references: [id])

  publishedAt DateTime?
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  // 关系
  tags      PostTag[]
  comments  Comment[]
  likes     Like[]
  favorites Favorite[]

  @@index([authorId])
  @@index([categoryId])
  @@index([status])
  @@index([type])
  @@index([publishedAt])
}

// 帖子标签关联表
model PostTag {
  postId String
  post   Post   @relation(fields: [postId], references: [id], onDelete: Cascade)
  tagId  String
  tag    Tag    @relation(fields: [tagId], references: [id], onDelete: Cascade)

  @@id([postId, tagId])
}

// 评论表（社区公开）
model Comment {
  id      String @id @default(cuid())
  content String @db.Text

  // 评论对象
  postId String
  post   Post   @relation(fields: [postId], references: [id], onDelete: Cascade)

  // 评论者
  userId     String
  user       User    @relation(fields: [userId], references: [id])
  userName   String? // 冗余字段
  userAvatar String? // 冗余字段

  // 回复
  parentId String?
  parent   Comment?  @relation("CommentTree", fields: [parentId], references: [id])
  replies  Comment[] @relation("CommentTree")

  // 状态
  isDeleted Boolean @default(false)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([postId])
  @@index([userId])
  @@index([parentId])
}

// 点赞表
model Like {
  userId    String
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  postId    String
  post      Post     @relation(fields: [postId], references: [id], onDelete: Cascade)
  createdAt DateTime @default(now())

  @@id([userId, postId])
}

// 收藏表
model Favorite {
  userId    String
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  postId    String
  post      Post     @relation(fields: [postId], references: [id], onDelete: Cascade)
  createdAt DateTime @default(now())

  @@id([userId, postId])
}

// 关注表
model Follow {
  followerId  String
  follower    User     @relation("Following", fields: [followerId], references: [id], onDelete: Cascade)
  followingId String
  following   User     @relation("Followers", fields: [followingId], references: [id], onDelete: Cascade)
  createdAt   DateTime @default(now())

  @@id([followerId, followingId])
  @@index([followerId])
  @@index([followingId])
}

// 资源表（社区公开资源）
model Resource {
  id            String  @id @default(cuid())
  title         String
  description   String?
  type          String // PDF, VIDEO, LINK, etc.
  url           String
  thumbnail     String?
  downloadCount Int     @default(0)
  fileSize      Int? // 文件大小（字节）

  // 分类
  categoryId String?
  category   Category? @relation(fields: [categoryId], references: [id])

  // 上传者
  uploaderId String
  uploader   User   @relation(fields: [uploaderId], references: [id])

  // 权限控制
  isPublic     Boolean @default(true)
  requireLogin Boolean @default(false) // 是否需要登录才能下载

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // 关系
  tags ResourceTag[]

  @@index([categoryId])
  @@index([uploaderId])
  @@index([isPublic])
}

// 资源标签关联表
model ResourceTag {
  resourceId String
  resource   Resource @relation(fields: [resourceId], references: [id], onDelete: Cascade)
  tagId      String
  tag        Tag      @relation(fields: [tagId], references: [id], onDelete: Cascade)

  @@id([resourceId, tagId])
}

// ==================== 社区系统 - 课程模块 ====================

// 课程分类表
model CourseCategory {
  id          String           @id @default(cuid())
  name        String
  slug        String           @unique
  description String?
  parentId    String?
  parent      CourseCategory?  @relation("CourseCategoryTree", fields: [parentId], references: [id])
  children    CourseCategory[] @relation("CourseCategoryTree")
  icon        String?
  order       Int              @default(0)
  isActive    Boolean          @default(true)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // 关系
  courses Course[]

  @@index([slug])
  @@index([parentId])
}

// 课程表
model Course {
  id           String  @id @default(cuid())
  title        String
  subtitle     String?
  description  String  @db.Text
  cover        String // 封面图
  previewVideo String? // 预览视频

  // 分类和标签
  categoryId String
  category   CourseCategory @relation(fields: [categoryId], references: [id])
  tags       String[]

  // 课程信息
  level        CourseLevel
  duration     Int // 总时长（分钟）
  lessonsCount Int         @default(0) // 课时数

  // 讲师信息
  instructorName   String
  instructorTitle  String?
  instructorAvatar String?
  instructorBio    String?

  // 价格和权限
  price         Decimal  @default(0) @db.Decimal(10, 2)
  originalPrice Decimal? @db.Decimal(10, 2)
  isFree        Boolean  @default(false)
  requireLogin  Boolean  @default(true)

  // 状态和统计
  status      CourseStatus @default(DRAFT)
  viewCount   Int          @default(0)
  enrollCount Int          @default(0)
  rating      Float? // 评分

  // SEO
  metaTitle       String?
  metaDescription String?
  metaKeywords    String[]

  publishedAt DateTime?
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  // 创建者（管理员）
  createdById String
  createdBy   AdminUser @relation(fields: [createdById], references: [id])

  // 关系
  chapters    CourseChapter[]
  enrollments CourseEnrollment[]
  reviews     CourseReview[]

  @@index([categoryId])
  @@index([status])
  @@index([isFree])
}

// 课程章节表
model CourseChapter {
  id       String @id @default(cuid())
  courseId String
  course   Course @relation(fields: [courseId], references: [id], onDelete: Cascade)

  title       String
  description String?
  order       Int

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // 关系
  lessons CourseLesson[]

  @@unique([courseId, order])
  @@index([courseId])
}

// 课程课时表
model CourseLesson {
  id        String        @id @default(cuid())
  chapterId String
  chapter   CourseChapter @relation(fields: [chapterId], references: [id], onDelete: Cascade)

  title       String
  description String?

  // 视频信息
  videoUrl      String
  videoDuration Int // 视频时长（秒）
  videoSize     Int? // 视频大小（字节）

  // 配置
  order  Int
  isFree Boolean @default(false) // 是否免费试看

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // 关系
  timestamps LessonTimestamp[]
  progress   LessonProgress[]

  @@unique([chapterId, order])
  @@index([chapterId])
}

// 课时时间戳表（时间点标记）
model LessonTimestamp {
  id       String       @id @default(cuid())
  lessonId String
  lesson   CourseLesson @relation(fields: [lessonId], references: [id], onDelete: Cascade)

  timestamp   Int // 时间点（秒）
  title       String
  description String?

  createdAt DateTime @default(now())

  @@index([lessonId])
  @@index([timestamp])
}

// 课程报名表
model CourseEnrollment {
  id       String @id @default(cuid())
  courseId String
  course   Course @relation(fields: [courseId], references: [id])
  userId   String
  user     User   @relation(fields: [userId], references: [id])

  // 报名信息
  enrolledAt   DateTime  @default(now())
  completedAt  DateTime?
  progress     Float     @default(0) // 完成进度 0-100
  lastAccessAt DateTime?

  // 支付信息（如果是付费课程）
  isPaid     Boolean   @default(false)
  paidAmount Decimal?  @db.Decimal(10, 2)
  paidAt     DateTime?

  @@unique([courseId, userId])
  @@index([userId])
  @@index([courseId])
}

// 课时学习进度表
model LessonProgress {
  id       String       @id @default(cuid())
  lessonId String
  lesson   CourseLesson @relation(fields: [lessonId], references: [id])
  userId   String
  user     User         @relation(fields: [userId], references: [id])

  // 进度信息
  watchedDuration Int       @default(0) // 已观看时长（秒）
  lastPosition    Int       @default(0) // 上次观看位置（秒）
  isCompleted     Boolean   @default(false)
  completedAt     DateTime?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([lessonId, userId])
  @@index([userId])
  @@index([lessonId])
}

// 课程评价表
model CourseReview {
  id       String @id @default(cuid())
  courseId String
  course   Course @relation(fields: [courseId], references: [id])
  userId   String
  user     User   @relation(fields: [userId], references: [id])

  rating  Int // 评分 1-5
  content String @db.Text

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([courseId, userId])
  @@index([courseId])
  @@index([userId])
}

// ==================== 社区系统 - 资源产品模块 ====================

// 产品分类表
model ProductCategory {
  id          String      @id @default(cuid())
  name        String
  slug        String      @unique
  description String?
  type        ProductType
  icon        String?
  order       Int         @default(0)
  isActive    Boolean     @default(true)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // 关系
  products Product[]

  @@index([slug])
  @@index([type])
}

// 产品表（研学营、证书等）
model Product {
  id         String          @id @default(cuid())
  name       String
  code       String          @unique // 产品编码
  type       ProductType
  categoryId String
  category   ProductCategory @relation(fields: [categoryId], references: [id])

  // 产品信息
  description String   @db.Text
  features    String[] // 产品特点
  highlights  Json? // 产品亮点（富文本）
  images      String[] // 产品图片
  brochureUrl String? // 产品手册

  // 价格信息
  price     Decimal? @db.Decimal(10, 2) // 标价
  priceUnit String? // 价格单位（人/期/证书等）
  priceNote String? // 价格说明

  // 产品详情
  duration        String? // 时长（如：7天6夜）
  location        String? // 地点
  startDate       DateTime? // 开始时间
  endDate         DateTime? // 结束时间
  capacity        Int? // 容量/名额
  minParticipants Int? // 最少成团人数

  // 适用对象
  targetAudience String[] // 目标受众
  ageRange       String? // 年龄范围
  gradeRange     String? // 年级范围

  // 合作方信息
  partnerId String?
  partner   Partner? @relation(fields: [partnerId], references: [id])

  // 状态
  status   ProductStatus @default(DRAFT)
  priority Int           @default(0) // 显示优先级

  // SEO
  metaTitle       String?
  metaDescription String?
  metaKeywords    String[]

  publishedAt DateTime?
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  // 创建者
  createdById String
  createdBy   AdminUser @relation(fields: [createdById], references: [id])

  // 关系
  inquiries ProductInquiry[]

  @@index([type])
  @@index([categoryId])
  @@index([status])
  @@index([partnerId])
}

// 合作方表
model Partner {
  id   String  @id @default(cuid())
  name String
  code String  @unique
  logo String?

  // 联系信息
  contactName  String
  contactPhone String
  contactEmail String
  address      String?

  // 合作信息
  cooperationType String[] // 合作类型
  description     String?

  isActive Boolean @default(true)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // 关系
  products Product[]

  @@index([code])
}

// 产品询价/意向单表
model ProductInquiry {
  id        String  @id @default(cuid())
  productId String
  product   Product @relation(fields: [productId], references: [id])

  // 询价方信息
  tenantId String?
  tenant   Tenant? @relation(fields: [tenantId], references: [id])
  userId   String?
  user     User?   @relation(fields: [userId], references: [id])

  // 联系信息（可能是非注册用户）
  contactName  String
  contactPhone String
  contactEmail String?
  organization String? // 机构名称

  // 询价详情
  participants Int? // 意向参与人数
  expectedDate DateTime? // 期望时间
  budget       String? // 预算范围
  requirements String?   @db.Text // 特殊需求
  message      String?   @db.Text // 留言

  // 处理信息
  status       InquiryStatus @default(PENDING)
  assignedToId String?
  assignedTo   AdminUser?    @relation(fields: [assignedToId], references: [id])

  // 跟进记录
  followUpNotes  Json? // 跟进记录
  lastContactAt  DateTime? // 最后联系时间
  nextFollowUpAt DateTime? // 下次跟进时间

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([productId])
  @@index([tenantId])
  @@index([userId])
  @@index([status])
  @@index([assignedToId])
}

// ==================== 工作区系统（租户隔离） ====================

// 规划师表
model Planner {
  id           String     @id @default(cuid())
  tenantUserId String     @unique
  tenantUser   TenantUser @relation(fields: [tenantUserId], references: [id])
  tenantId     String
  tenant       Tenant     @relation(fields: [tenantId], references: [id])

  // 专业信息
  title        String? // 职称
  specialties  String[] // 专长领域
  experience   Int? // 从业年限
  introduction String?  @db.Text

  // 统计信息
  totalStudents Int    @default(0)
  successRate   Float? // 成功率
  rating        Float? // 评分

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // 关系
  students     StudentPlanner[]
  plans        Plan[]
  appointments Appointment[]

  @@index([tenantId])
}

// 学生表
model Student {
  id           String      @id @default(cuid())
  tenantUserId String?     @unique
  tenantUser   TenantUser? @relation(fields: [tenantUserId], references: [id])
  tenantId     String
  tenant       Tenant      @relation(fields: [tenantId], references: [id])

  // 基本信息
  name     String
  gender   String?
  birthday DateTime?
  phone    String?
  email    String?
  avatar   String?

  // 学业信息
  school String?
  grade  String?
  major  String?
  gpa    Float?
  rank   Int?

  // 家庭信息
  parentName       String?
  parentPhone      String?
  parentEmail      String?
  familyBackground String? @db.Text

  // 标签和备注
  tags  String[]
  notes String?  @db.Text

  // 状态
  status String @default("ACTIVE") // ACTIVE, GRADUATED, INACTIVE

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // 关系
  planners     StudentPlanner[]
  plans        Plan[]
  appointments Appointment[]
  assessments  Assessment[]
  documents    Document[]

  @@index([tenantId])
  @@index([status])
}

// 学生-规划师关联表
model StudentPlanner {
  studentId  String
  student    Student  @relation(fields: [studentId], references: [id], onDelete: Cascade)
  plannerId  String
  planner    Planner  @relation(fields: [plannerId], references: [id], onDelete: Cascade)
  isPrimary  Boolean  @default(false) // 是否主规划师
  assignedAt DateTime @default(now())

  @@id([studentId, plannerId])
}

// 规划方案表
model Plan {
  id          String  @id @default(cuid())
  title       String
  description String?
  content     Json // 详细规划内容
  type        String // 规划类型：升学规划、职业规划等

  // 关联
  studentId String
  student   Student @relation(fields: [studentId], references: [id])
  plannerId String
  planner   Planner @relation(fields: [plannerId], references: [id])

  // 时间范围
  startDate DateTime
  endDate   DateTime

  // 状态
  status String @default("DRAFT") // DRAFT, ACTIVE, COMPLETED, CANCELLED

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // 关系
  milestones Milestone[]

  @@index([studentId])
  @@index([plannerId])
  @@index([status])
}

// 里程碑表
model Milestone {
  id            String    @id @default(cuid())
  planId        String
  plan          Plan      @relation(fields: [planId], references: [id], onDelete: Cascade)
  title         String
  description   String?
  targetDate    DateTime
  completedDate DateTime?
  status        String    @default("PENDING") // PENDING, IN_PROGRESS, COMPLETED, CANCELLED
  order         Int

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([planId])
  @@index([status])
}

// 预约表
model Appointment {
  id        String  @id @default(cuid())
  studentId String
  student   Student @relation(fields: [studentId], references: [id])
  plannerId String
  planner   Planner @relation(fields: [plannerId], references: [id])

  // 预约信息
  title       String
  description String?
  startTime   DateTime
  endTime     DateTime
  location    String? // 地点或线上会议链接
  type        String // OFFLINE, ONLINE, PHONE

  // 状态
  status String @default("PENDING") // PENDING, CONFIRMED, COMPLETED, CANCELLED

  // 记录
  notes   String? @db.Text
  summary String? @db.Text

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([studentId])
  @@index([plannerId])
  @@index([startTime])
  @@index([status])
}

// AI工具配置表
model AITool {
  id          String  @id @default(cuid())
  name        String
  code        String  @unique // 工具唯一标识
  description String?
  icon        String?
  category    String // 工具分类

  // 配置
  config  Json // API配置、参数等
  prompts Json? // 预设提示词

  // 权限和限制
  requiredRoles UserRole[]
  dailyLimit    Int? // 每日使用限制

  // 租户关系
  tenantId String?
  tenant   Tenant? @relation(fields: [tenantId], references: [id])
  isGlobal Boolean @default(false) // 是否全局工具

  isActive Boolean @default(true)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // 关系
  usageRecords AIUsageRecord[]

  @@index([code])
  @@index([tenantId])
}

// AI使用记录表
model AIUsageRecord {
  id           String     @id @default(cuid())
  toolId       String
  tool         AITool     @relation(fields: [toolId], references: [id])
  tenantUserId String
  tenantUser   TenantUser @relation(fields: [tenantUserId], references: [id])

  // 使用详情
  input    Json
  output   Json?
  tokens   Int? // 消耗的token数
  duration Int? // 处理时长（毫秒）
  status   String // SUCCESS, FAILED, TIMEOUT
  error    String?

  createdAt DateTime @default(now())

  @@index([toolId])
  @@index([tenantUserId])
  @@index([createdAt])
}

// 评估测试表
model Assessment {
  id        String  @id @default(cuid())
  studentId String
  student   Student @relation(fields: [studentId], references: [id])

  // 测试信息
  type        String // 测试类型：性格测试、兴趣测试等
  name        String
  description String?
  questions   Json // 问题列表
  answers     Json? // 答案
  result      Json? // 测试结果

  // 状态
  status      String    @default("PENDING") // PENDING, IN_PROGRESS, COMPLETED
  completedAt DateTime?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([studentId])
  @@index([type])
  @@index([status])
}

// 文档表
model Document {
  id        String  @id @default(cuid())
  studentId String
  student   Student @relation(fields: [studentId], references: [id])

  // 文档信息
  title    String
  type     String // 文档类型：成绩单、推荐信等
  category String
  fileUrl  String
  fileSize Int
  mimeType String

  // 元数据
  metadata Json?
  tags     String[]

  uploaderId String
  uploader   TenantUser @relation(fields: [uploaderId], references: [id])

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([studentId])
  @@index([type])
  @@index([category])
}

// ==================== 任务系统（租户隔离） ====================

// 定时任务表
model Task {
  id     String     @id @default(cuid())
  name   String
  type   TaskType
  status TaskStatus @default(PENDING)

  // 任务配置
  config   Json // 任务具体配置
  schedule String? // cron表达式

  // 执行信息
  lastRunAt DateTime?
  nextRunAt DateTime?
  runCount  Int       @default(0)

  // 租户关系
  tenantId String
  tenant   Tenant @relation(fields: [tenantId], references: [id])

  createdById String
  createdBy   TenantUser @relation(fields: [createdById], references: [id])

  isActive Boolean @default(true)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // 关系
  executions TaskExecution[]

  @@index([tenantId])
  @@index([type])
  @@index([status])
  @@index([nextRunAt])
}

// 任务执行记录表
model TaskExecution {
  id     String @id @default(cuid())
  taskId String
  task   Task   @relation(fields: [taskId], references: [id], onDelete: Cascade)

  status      TaskStatus
  startedAt   DateTime
  completedAt DateTime?
  duration    Int? // 执行时长（毫秒）

  // 执行结果
  result Json?
  error  String?

  @@index([taskId])
  @@index([startedAt])
}

// ==================== 营销系统（租户隔离） ====================

// 落地页表
model LandingPage {
  id          String  @id @default(cuid())
  title       String
  slug        String  @unique
  description String?

  // 页面内容
  template  String // 使用的模板
  content   Json // 页面配置和内容
  customCss String? @db.Text
  customJs  String? @db.Text

  // SEO
  metaTitle       String?
  metaDescription String?
  metaKeywords    String[]

  // 统计
  viewCount       Int @default(0)
  conversionCount Int @default(0)

  // 租户关系
  tenantId String
  tenant   Tenant @relation(fields: [tenantId], references: [id])

  // 状态
  isPublished Boolean   @default(false)
  publishedAt DateTime?

  createdById String
  createdBy   TenantUser @relation(fields: [createdById], references: [id])

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // 关系
  forms Form[]

  @@index([slug])
  @@index([tenantId])
  @@index([isPublished])
}

// 表单表
model Form {
  id          String  @id @default(cuid())
  name        String
  description String?

  // 表单配置
  fields         Json // 表单字段配置
  submitAction   String // 提交后的动作
  successMessage String?

  // 关联页面
  pageId String?
  page   LandingPage? @relation(fields: [pageId], references: [id])

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // 关系
  submissions FormSubmission[]

  @@index([pageId])
}

// 表单提交记录表
model FormSubmission {
  id     String @id @default(cuid())
  formId String
  form   Form   @relation(fields: [formId], references: [id])

  // 提交数据
  data Json

  // 提交者信息
  ip        String?
  userAgent String?
  referrer  String?

  // 处理状态
  isProcessed Boolean   @default(false)
  processedAt DateTime?
  notes       String?

  createdAt DateTime @default(now())

  @@index([formId])
  @@index([createdAt])
}

// ==================== 通知系统 ====================

// 通知表
model Notification {
  id     String @id @default(cuid())
  userId String
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  type    NotificationType
  title   String
  content String

  // 关联数据
  relatedId   String? // 关联数据ID
  relatedType String? // 关联数据类型

  // 状态
  isRead Boolean   @default(false)
  readAt DateTime?

  createdAt DateTime @default(now())

  @@index([userId])
  @@index([isRead])
  @@index([type])
}

// ==================== 管理后台系统 ====================

// 管理员用户表
model AdminUser {
  id       String  @id @default(cuid())
  email    String  @unique
  username String  @unique
  password String
  name     String
  avatar   String?
  phone    String?

  role   UserRole // SUPER_ADMIN, ADMIN, OPERATOR
  status AdminStatus @default(ACTIVE)

  // 权限配置
  permissions    String[] // 具体权限列表
  allowedModules String[] // 允许访问的模块

  lastLoginAt DateTime?
  lastLoginIp String?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // 关系
  sessions      AdminSession[]
  operationLogs OperationLog[]
  courses       Course[]
  products      Product[]
  inquiries     ProductInquiry[]
  announcements SystemAnnouncement[]

  @@index([email])
  @@index([role])
}

// 管理员会话表
model AdminSession {
  id      String    @id @default(cuid())
  adminId String
  admin   AdminUser @relation(fields: [adminId], references: [id], onDelete: Cascade)

  token     String  @unique
  ip        String?
  userAgent String?

  expiresAt DateTime
  createdAt DateTime @default(now())

  @@index([adminId])
  @@index([token])
}

// 权限定义表
model Permission {
  id          String  @id @default(cuid())
  code        String  @unique // 权限代码
  name        String // 权限名称
  description String?
  module      String // 所属模块

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // 关系
  roles RolePermission[]

  @@index([module])
}

// 角色表
model Role {
  id          String  @id @default(cuid())
  code        String  @unique
  name        String
  description String?

  isSystem Boolean @default(false) // 是否系统角色

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // 关系
  permissions RolePermission[]
}

// 角色权限关联表
model RolePermission {
  roleId       String
  role         Role       @relation(fields: [roleId], references: [id], onDelete: Cascade)
  permissionId String
  permission   Permission @relation(fields: [permissionId], references: [id], onDelete: Cascade)

  @@id([roleId, permissionId])
}

// 操作日志表（管理后台）
model OperationLog {
  id      String    @id @default(cuid())
  adminId String
  admin   AdminUser @relation(fields: [adminId], references: [id])

  module     String // 操作模块
  action     String // 操作类型
  targetType String // 操作对象类型
  targetId   String // 操作对象ID

  // 详细信息
  details Json? // 操作详情
  changes Json? // 数据变更

  ip        String?
  userAgent String?

  createdAt DateTime @default(now())

  @@index([adminId])
  @@index([module])
  @@index([targetType, targetId])
  @@index([createdAt])
}

// 系统公告表
model SystemAnnouncement {
  id      String @id @default(cuid())
  title   String
  content String @db.Text
  type    String // INFO, WARNING, ERROR, SUCCESS

  // 目标受众
  targetRoles     UserRole[] // 目标角色
  targetTenantIds String[] // 特定租户（空数组表示所有）

  // 显示配置
  priority Int     @default(0)
  isPopup  Boolean @default(false) // 是否弹窗显示

  // 时间配置
  startTime DateTime
  endTime   DateTime

  createdById String
  createdBy   AdminUser @relation(fields: [createdById], references: [id])

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([startTime, endTime])
  @@index([targetRoles])
}

// 系统监控表
model SystemMonitor {
  id        String   @id @default(cuid())
  timestamp DateTime @default(now())

  // 系统指标
  cpuUsage    Float?
  memoryUsage Float?
  diskUsage   Float?

  // 业务指标
  activeUsers   Int
  activeTenants Int
  apiRequests   Int
  errorCount    Int

  // 详细数据
  details Json?

  @@index([timestamp])
}

// ==================== 系统管理 ====================

// 操作日志表（工作区）
model AuditLog {
  id           String     @id @default(cuid())
  tenantUserId String
  tenantUser   TenantUser @relation(fields: [tenantUserId], references: [id])

  action   String // 操作类型
  entity   String // 操作实体
  entityId String // 实体ID

  // 详细信息
  changes   Json? // 变更内容
  ip        String?
  userAgent String?

  createdAt DateTime @default(now())

  @@index([tenantUserId])
  @@index([entity, entityId])
  @@index([createdAt])
}

// 系统配置表
model SystemConfig {
  id          String  @id @default(cuid())
  key         String  @unique
  value       Json
  description String?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([key])
}
