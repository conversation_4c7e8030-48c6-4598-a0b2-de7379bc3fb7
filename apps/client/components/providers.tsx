"use client";

import * as React from "react";
import { ThemeProvider as NextThemesProvider } from "next-themes";
import { UserProvider } from "../contexts/UserContext";
import { PermissionProvider } from "../contexts/PermissionContext";
import { Toaster } from "@workspace/ui/components/sonner";

export function Providers({ children }: { children: React.ReactNode }) {
  return (
    <NextThemesProvider
      attribute="class"
      defaultTheme="system"
      enableSystem
      disableTransitionOnChange
      enableColorScheme
    >
      <UserProvider>
        <PermissionProvider>
          {children}
          <Toaster richColors />
        </PermissionProvider>
      </UserProvider>
    </NextThemesProvider>
  );
}
